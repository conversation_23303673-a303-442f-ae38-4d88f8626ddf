<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\User;

use App\AdminModule\Presenters\BasePresenter;
use App\AdminModule\Presenters\User\Components\DataGrid\DataGrid;
use App\AdminModule\Presenters\User\Components\DataGrid\DataGridFactory;
use App\AdminModule\Presenters\User\Components\Form\Form;
use App\AdminModule\Presenters\User\Components\Form\FormFactory;
use App\AdminModule\Presenters\User\Components\ShellForm\ShellForm;
use App\AdminModule\Presenters\User\Components\ShellForm\ShellFormFactory;
use App\Model\Orm\User\User;

/**
 * @property User $object
 */
final class UserPresenter extends BasePresenter
{

	private User $object;

	public function __construct(
		private readonly DataGridFactory $dataGridFactory,
		private readonly FormFactory $formFactory,
		private readonly ShellFormFactory $shellFormFactory,
	)
	{
		parent::__construct();
	}


	public function actionEdit(int $id): void
	{
		$users = $this->orm->user->findBy([]);
		$object = $users->getById($id);
		if ($object === null) {
			$this->redirect('default');
		}

		$this->object = $object;
	}


	protected function createComponentForm(): Form
	{
		return $this->formFactory->create($this->object, $this->userEntity);
	}

	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create();
	}


	protected function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create();
	}

}

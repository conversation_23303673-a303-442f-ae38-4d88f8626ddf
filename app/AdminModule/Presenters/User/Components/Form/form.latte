{varType App\Model\Orm\User\User $userEntity}
<form n:name="form" class="main__main" data-controller="pagemenu">
	<div class="main__header">
		{include $templates.'/part/box/header.latte',
			props: [
				hrefClose: 'default',
				img: $imgSrc,
				title: $userEntity->name,
				hasGeneratedMenu: true,
				isPageTitle: true,
			]
		}
	</div>
	<div class="main__content scroll">

		{foreach $flashes as $flash}
			<div class="message message-{$flash->type}">{$flash->message}</div>
		{/foreach}
		<br>
		{include './parts/content/bio.latte', form => $form}
		{include './parts/content/password.latte', form => $form}
		{include './parts/content/addresses.latte', form => $form, customAddresses => (array)$userEntity->customAddress}

		{include $corePartsDirectory .'/content/custom-fields.latte',
			form => $form,
			cfObject => $userEntity,
			title => 'Doplňkové informace',
			containerName => 'setup',
			itemName => 'cf'
		}


			{include './parts/overlay/editItem.latte', form=>$form}
	</div>

	<div class="main__content-side scroll">
		<p>
			<button n:name="send" class="btn btn--full btn--success">
				<span class="btn__text item-icon">
					<span class="item-icon__icon icon">
						{include $templates.'/part/icons/save.svg'}
					</span>
					<span class="item-icon__text">
						Uložit
					</span>
				</span>
			</button>
		</p>

		<p {*n:if="$userEntity->status !== App\Model\Orm\User\User::STATUS_APPROVED"*}>
			<a n:href="approve!" class="btn btn--full btn--grey"  data-controller="Confirm" data-confirm-message-value="Opravdu chcete schválit uživatele?" data-action="Confirm#confirm">
				<span class="btn__text item-icon">
					<span class="item-icon__icon icon">
						{include $templates.'/part/icons/check.svg'}
					</span>
					<span class="item-icon__text">
						Schválit uživatele
					</span>
				</span>
			</a>
		</p>

		<p>
			<a n:href="delete!" class="btn btn--full btn--grey btn--remove" data-controller="Confirm" data-confirm-message-value="Smazat položku?" data-action="Confirm#confirm">
				<span class="btn__text item-icon">
					<span class="item-icon__icon icon">
						{include $templates.'/part/icons/trash.svg'}
					</span>
					<span class="item-icon__text">
						Smazat
					</span>
				</span>
			</a>
		</p>
	</div>

	{include './parts/overlay/editItem.latte', form=>$form}


	{capture $templateTargets}
		{include './parts/newItemTemplate.latte', form=>$form}
		{include $templates . '/part/core/libraryOverlay.latte'}
	{/capture}

</form>
{$templateTargets|noescape}




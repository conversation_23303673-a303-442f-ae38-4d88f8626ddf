<?php

declare(strict_types=1);

namespace App\AdminModule\Presenters\User\Components\Form;

use App\Model\ConfigService;
use App\Model\CustomField\SuggestUrls;
use App\Model\Orm\Mutation\MutationRepository;
use App\Model\Orm\Orm;
use App\Model\Orm\User\User;
use App\Model\Orm\User\UserModel;
use App\Model\Orm\User\UserRepository;
use App\Model\Translator;
use App\Model\TranslatorDB;
use App\AdminModule\Presenters\User\Components\Form\FormData\BaseFormData;
use Nette\Application\UI\Control;
use Nette\Http\IRequest;
use const RS_TEMPLATE_DIR;

final class Form extends Control
{
	private array $postData = [];

	public function __construct(
		private readonly User $user,
		private readonly User $admin,
		private readonly Builder $formBuilder,
		private readonly Handler $formHandler,
		private readonly SuggestUrls $urls,
		private readonly Translator $translator,
		private readonly TranslatorDB $translatorDB,
		private readonly Orm $orm,
		private readonly ConfigService $configService,
		private readonly MutationRepository $mutationRepository,
		private readonly UserRepository $userRepository,
		private readonly UserModel $userModel,
	)
	{
		$this->onAnchor[] = $this->init(...);
	}

	private function init(): void
	{
		$method = $this->getPresenter()->request->getMethod();

		if ($method === IRequest::Post) {
			$this->postData = $this->getPresenter()->request->getPost();
		}
	}

	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
		$template->add('templates', RS_TEMPLATE_DIR);
		$template->add('userEntity', $this->user);
		$template->add('imgSrc', '');
		$template->add('corePartsDirectory', \App\PostType\Core\AdminModule\Components\Form\Form::TEMPLATE_PARTS_DIR);

		$template->customAdresses =  $this->user->customAddress;
		$template->orm = $this->orm;
		$template->translatorDB = $this->translatorDB;
		$template->fileUploadLink = $this->presenter->link(':Admin:File:upload');
		$template->mutation = $this->mutationRepository->getRsDefault();

		$template->config = $this->configService->getParams();
		$template->admin = $this->admin;

		$template->urls = $this->urls;
		$template->render(__DIR__ . '/form.latte');
	}

	protected function createComponentForm(): \Nette\Application\UI\Form
	{
		$form = new \Nette\Application\UI\Form();
		$form->setMappedType(BaseFormData::class);


		$this->formBuilder->build($form, $this->user, $this->admin, $this->postData);
		$form->setTranslator($this->translator);
		$form->onSuccess[] = $this->formSucceeded(...);
		$form->onValidate[] = $this->formValidate(...);
		$form->onError[] = $this->formError(...);

		return $form;
	}

	public function formValidate(\Nette\Application\UI\Form $form, BaseFormData $values): void
	{
		if ($this->user->email !== $values->email) { // pri add nebo zmene emailu
			$user = $this->userRepository->getByEmail($values->email);
			if ($user !== null) {
				$form['email']->addError('mail_exist');
				$form->addError('mail_exist');
			}
		}

		if ($values->password !== $values->passwordVerify) {
			$form['password']->setDefaultValue('')->addError('msg_password_not_same');
			$form['passwordVerify']->setDefaultValue('')->addError('msg_password_not_same');
			$form->addError('msg_password_not_same');
		}
	}

	private function formSucceeded(\Nette\Application\UI\Form $form, BaseFormData $data): void
	{
		$this->formHandler->handle($this->user, $data, $this->admin);
		$this->presenter->redirect('edit', ['id' => $this->user->id]);
	}

	private function formError(\Nette\Application\UI\Form $form): void
	{
		foreach ($form->errors as $error) {
			$this->flashMessage($error, 'error');
		}
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	public function handleApprove(): never
	{
		/*$mutation = $this->user->mutations->toCollection()->fetch();
		if ($mutation !== null) {
			$this->statusStateMachineFactory->create($this->user, $mutation)->apply(StatusStateMachine::TRANSITION_APPROVE);
		}*/

		$this->presenter->redirect('this');
	}


	public function handleDelete(): never
	{
		$this->userModel->delete($this->user);
		$this->presenter->redirect('default');
	}

}

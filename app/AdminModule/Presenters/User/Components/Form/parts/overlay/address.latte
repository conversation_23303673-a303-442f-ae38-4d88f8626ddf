{formContainer $addressContainer}
	{embed $templates.'/part/box/toggle.latte', props=>[
		title: '',
		id: 'settings-address-'.$addressId,
		open: true
	], templates=>$templates}
		{block content}
				<div>
					{include $templates.'/part/core/inp.latte' props: [
						input: $addressContainer['addressTitle'],
						classesLabel: ['title'],
					]}
					{include $templates.'/part/core/inp.latte' props: [
						input: $addressContainer['addressNote'],
						classesLabel: ['title'],
					]}
					{include $templates.'/part/core/checkbox.latte' props: [
						input: $addressContainer['isDefault'],
						type: 'checkbox',
						classesLabel: ['title'],
					]}
				</div>
		{/block}
	{/embed}
	{embed $templates.'/part/box/toggle.latte', props=>[
		title: 'Adresa zákazníka',
		id: 'customer-address-'.$addressId,
		open: true
	], templates=>$templates}
		{block content}
				<div>
					{include $templates.'/part/core/inp.latte' props: [
						input: $addressContainer['invFirstname'],
						classesLabel: ['title'],
					]}
					{include $templates.'/part/core/inp.latte' props: [
						input: $addressContainer['invLastname'],
						classesLabel: ['title'],
					]}
					{include $templates.'/part/core/inp.latte' props: [
						input: $addressContainer['invStreet'],
						classesLabel: ['title'],
					]}
					{include $templates.'/part/core/inp.latte' props: [
						input: $addressContainer['invCity'],
						classesLabel: ['title'],
					]}
					{include $templates.'/part/core/inp.latte' props: [
						input: $addressContainer['invZip'],
						classesLabel: ['title'],
					]}
				</div>
		{/block}
	{/embed}
	{embed $templates.'/part/box/toggle.latte', props=>[
		title: 'Fakturační údaje',
		id: 'accounting-data-'.$addressId,
		open: true
	], templates=>$templates}
		{block content}
				<div>
					{include $templates.'/part/core/inp.latte' props: [
						input: $addressContainer['invCompany'],
						classesLabel: ['title'],
					]}
					{include $templates.'/part/core/inp.latte' props: [
						input: $addressContainer['invIc'],
						classesLabel: ['title'],
					]}
					{include $templates.'/part/core/inp.latte' props: [
						input: $addressContainer['invDic'],
						classesLabel: ['title'],
					]}
				</div>
		{/block}
	{/embed}
	{embed $templates.'/part/box/toggle.latte', props=>[
		title: 'Dodací adresa',
		id: 'delivery-address-'.$addressId,
		open: true
	], templates=>$templates}
		{block content}
				<div>
					{include $templates.'/part/core/inp.latte' props: [
						input: $addressContainer['delFirstname'],
						classesLabel: ['title'],
					]}
					{include $templates.'/part/core/inp.latte' props: [
						input: $addressContainer['delLastname'],
						classesLabel: ['title'],
					]}

					{include $templates.'/part/core/inp.latte' props: [
						input: $addressContainer['delStreet'],
						classesLabel: ['title'],
					]}
					{include $templates.'/part/core/inp.latte' props: [
						input: $addressContainer['delCity'],
						classesLabel: ['title'],
					]}
					{include $templates.'/part/core/inp.latte' props: [
						input: $addressContainer['delZip'],
						classesLabel: ['title'],
					]}
				</div>
		{/block}
	{/embed}
{/formContainer}

<div data-Templates-target="overlays">
	{foreach $form['customAddress']->getComponents() as $addressId=>$addressContainer}
		{continueIf $addressId === 'newItemMarker'}

		{embed $templates.'/part/core/overlay.latte', props: [
		id: 'address_' . $addressId,
		title: 'Editace adresy',
		data: [
		controller: 'CustomAddressEdit',
		CustomAddressEdit-id-value: 'address_' . $addressId,
		],
		], templates=>$templates}
			{block content}
				{include './address.latte', addressId=>$addressId, addressContainer=>$addressContainer}
			{/block}
		{/embed}
	{/foreach}
</div>

{var $anchorName = 'bio'}
{var $icon = $templates . '/part/icons/cog.svg'}
{var $title = 'Osobní údaje'}

{var $props = [
	title: $title,
	id: $anchorName,
	icon: $icon,
	variant: 'main',
	open: true,
	classes: ['u-mb-xxs'],
]}

{embed $templates . '/part/box/toggle.latte', props => $props, templates => $templates}
	{block content}
		{varType App\Model\Orm\User\User $userEntity}
{*		{if $admin->isDeveloper()}*}
{*			{include $templates . '/part/core/inp.latte', props: [*}
{*				input: $form['name'],*}
{*				classesLabel: ['title'],*}
{*			]}*}
{*		{/if}*}

		{include $templates . '/part/core/inp.latte', props: [
			input: $form['email'],
			classesLabel: ['title'],
		]}

		{ifset $form['role']}
			{include $templates . '/part/core/inp.latte', props: [
				input: $form['role'],
				type: 'select',
				classesLabel: ['title'],
			]}
		{/ifset}

		{include $templates . '/part/core/inp.latte', props: [
			input: $form['priceLevel'],
			type: 'select',
			classesLabel: ['title'],
		]}

		{include $templates . '/part/core/inp.latte', props: [
			input: $form['firstname'],
			classesLabel: ['title'],
		]}
		{include $templates . '/part/core/inp.latte', props: [
			input: $form['lastname'],
			classesLabel: ['title'],
		]}
		{include $templates . '/part/core/inp.latte', props: [
			input: $form['phone'],
			classesLabel: ['title'],
		]}

		{include $templates . '/part/core/inp.latte', props: [
			input: $form['street'],
			classesLabel: ['title'],
		]}

		{include $templates . '/part/core/inp.latte', props: [
			input: $form['city'],
			classesLabel: ['title'],
		]}

		{include $templates . '/part/core/inp.latte', props: [
			input: $form['zip'],
			classesLabel: ['title'],
		]}

		{*{include $templates . '/part/core/inp.latte', props: [
			input: $form['ic'],
			classesLabel: ['title'],
		]}
		{include $templates . '/part/core/inp.latte', props: [
			input: $form['company'],
			classesLabel: ['title'],
		]}*}

		{include $templates . '/part/core/inp.latte', props: [
			input: $form['state'],
			type: 'select',
			classesLabel: ['title'],
		]}


	{/block}
{/embed}

{var $props = [
	title: 'Adresy',
	id: 'addresses',
	icon: $templates.'/part/icons/coins.svg',
	variant: 'main',
	classes: ['u-mb-xxs']
]}

{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
	{block content}

		{var $items = []}

		{foreach $form['customAddress']->getComponents() as $addressKey=>$addressContainer}
			{continueIf $addressKey === 'newItemMarker'}

			{var $address = $customAddresses[$addressKey]}

			{var $itemName = sprintf('Název: %s | Adresa: %s, %s %s', $address->addressTitle ?? '', $address->invStreet, $address->invZip, $address->invCity)}
			{var $item = [
				data: [
					removeitem-target: 'item',
					controller: 'CustomAddress RemoveItem',
					action: 'CustomAddressEdit:updateValues@window->CustomAddress#updateValues',
					CustomAddress-id-value: 'address_' . $addressKey,
				],
				texts: [
					[
						text: '<span data-CustomAddress-target="name">'.$itemName.'</span><span data-CustomAddress-target="address">'.'</span>',
					]
				],
				btnsAfter: [
					[
						icon: $templates.'/part/icons/pencil-alt.svg',
						tooltip: 'Editovat',
						extend: true,
						data: [
							controller: 'Toggle',
							action: 'Toggle#changeClass CustomAddress#edit',
							toggle-target-value: '#overlay-address_'.$addressKey,
							toggle-target-class-value: 'is-visible'
						]
					],
					[
						icon: $templates.'/part/icons/trash.svg',
						tooltip: 'Odstranit',
						variant: 'remove',
						data: [
							action: 'RemoveItem#remove CustomAddress#remove'
						]
					]
				]
			]}

			{php $items[] = $item}
		{/foreach}

		{include $templates.'/part/box/list.latte',
			props: [
				data: [
					controller: 'List',
					List-name-value: 'address',
				],
				listData: [
					List-target: 'list',
				],
				addData: [
					action: 'List#add',
				],
				add: true,
				dragdrop: false,
				items: $items
			]
		}
	{/block}
{/embed}












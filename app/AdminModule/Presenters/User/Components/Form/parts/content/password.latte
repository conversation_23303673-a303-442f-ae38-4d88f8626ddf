{var $anchorName = 'content'}
{var $icon = $templates . '/part/icons/align-left.svg'}
{var $title = '<PERSON><PERSON>lo'}

{var $props = [
	title: $title,
	id: $anchorName,
	icon: $icon,
	variant: 'main',
	open: true,
	classes: ['u-mb-xxs'],
]}


{if isset($form['password']) && isset($form['passwordVerify'])}
	{embed $templates . '/part/box/toggle.latte', props => $props, templates => $templates}
		{block content}
			{include $templates . '/part/core/inp.latte', props: [
				input: $form['password'],
				classesLabel: ['title'],
				type: password,
			]}
			{include $templates . '/part/core/inp.latte', props: [
				input: $form['passwordVerify'],
				classesLabel: ['title'],
				type: password,
			]}
		{/block}
	{/embed}
{/if}

<div class="u-hide">
	<div data-Templates-target="addressItem">
		{include $templates.'/part/box/list-item.latte',
		props: [
			dragdrop: false,
			data: [
				controller: 'CustomAddress RemoveItem',
				action: 'CustomAddressEdit:updateValues@window->CustomAddress#updateValues',
				removeitem-target: 'item',
				CustomAddress-id-value: 'newItemMarker',
			],
			texts: [
				[
				text: '[<span data-CustomAddress-target="name">Nová adresa</span>]',
				]
			],
			btnsAfter: [
					[
						icon: $templates.'/part/icons/pencil-alt.svg',
						tooltip: 'Editovat',
						data: [
							controller: 'Toggle',
							action: 'Toggle#changeClass CustomAddress#edit',
							toggle-target-value: '#overlay-newItemMarker',
							toggle-target-class-value: 'is-visible'
						]
					],
					[
						icon: $templates.'/part/icons/trash.svg',
						tooltip: 'Odstranit',
						variant: 'remove',
						data: [
							action: 'RemoveItem#remove CustomAddress#remove',
						]
					]
				]
			]
		}
	</div>

	<div data-Templates-target="addressOverlay">

		{embed $templates.'/part/core/overlay.latte', props: [
		id: 'newItemMarker',
		title: 'Editace / Přidání adresy',
		data: [
			controller: 'CustomAddressEdit',
			CustomAddressEdit-id-value: 'newItemMarker',
		],
		classes: ['is-visible'],
		], templates=>$templates}
			{block content}
				{include './overlay/address.latte', addressId: 'newItemMarker', addressContainer: $form['customAddress']['newItemMarker']}
			{/block}
		{/embed}
	</div>
</div>

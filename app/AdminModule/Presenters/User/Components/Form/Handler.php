<?php

declare(strict_types=1);

namespace App\AdminModule\Presenters\User\Components\Form;

use App\AdminModule\Presenters\User\Components\Form\FormData\BaseFormData;
use App\Model\CustomField\CustomFields;
use App\Model\Orm\Admin\Admin;
use App\Model\Orm\User\User;
use App\Model\Orm\User\UserRepository;
use Nette\Security\Passwords;
use Nette\Utils\ArrayHash;

final class Handler
{

	public function __construct(
		private readonly UserRepository $userRepository,
		private readonly CustomFields $customFields,
		private readonly Passwords $passwords,
	)
	{
	}

	public function handle(User $user, BaseFormData $data, User $admin): void
	{
		$user->email = $data->email;
		$user->firstname = $data->firstname;
		$user->lastname = $data->lastname;
		if ($data->role !== null) {
			$user->role = $data->role;
		}
		$user->phone = $data->phone;
		$user->ic = $data->ic;
		$user->company = $data->company;
		$user->state = $data->state;
		$user->priceLevel = $data->priceLevel;

		unset($data->customAddress['newItemMarker']);
		$user->customAddress = $data->customAddress;
		$user->editedTime = 'now';

		if ($data->password !== '') {
			$user->password = $this->passwords->hash($data->password);
		}

		if (isset($user->cf)) {
			if (isset($data->setup->cf) && $data->setup->cf !== '') {
				$user->setCf($this->customFields->prepareDataToSave($data->setup->cf));
			} else {
				$user->setCf(new ArrayHash());
			}
		}
		$this->userRepository->persistAndFlush($user);
	}

}

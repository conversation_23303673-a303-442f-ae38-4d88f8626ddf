<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\User\Components\ShellForm;

use App\AdminModule\Presenters\User\Components\ShellForm\FormData\BaseFormData;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Mutation\MutationRepository;
use App\Model\Orm\Orm;
use App\Model\Orm\User\User;
use App\Model\Orm\User\UserModel;
use App\Model\Translator;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nextras\Dbal\Drivers\Exception\UniqueConstraintViolationException;
use Nextras\Orm\Collection\ICollection;
use Throwable;

class ShellForm extends Control
{

	/** @var Icollection<Mutation> */
	private ICollection $mutations;

	public function __construct(
		private readonly Translator $translator,
		private readonly MutationRepository $mutationRepository,
		private readonly Orm $orm,
		private readonly UserModel $userModel,
		private readonly \App\Model\Security\User $user,
	)
	{
		$this->onAnchor[] = [$this, 'init'];
	}


	public function init(): void
	{
		$this->mutations = $this->orm->mutation->findAllWithRsOrder();
	}

	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
		$template->add('templates', RS_TEMPLATE_DIR);
		$template->add('mutations', $this->mutations);

		$template->render(__DIR__ . '/shellForm.latte');
	}


	protected function createComponentForm(): Form
	{
		$form = new Form();
		$form->setMappedType(BaseFormData::class);
		$form->setTranslator($this->translator);

		$form->addEmail('email', 'email')->setRequired();
		if ($this->user->isInRole(User::ROLE_ADMIN) || $this->user->isInRole(User::ROLE_DEVELOPER)) {
			$form->addSelect('role', 'role', User::getConstsByPrefix('ROLE_'));
		}
		$form->addSelect('mutation', 'select_mutation', $this->mutations->fetchPairs('id', 'name'));
		$form->addSubmit('send', 'send');

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];
		return $form;
	}


	public function formError(Form $form): void
	{
		$this->flashMessage('Error', 'error');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function formSucceeded(Form $form, BaseFormData $data): void
	{
		$newUser = null;
		try {
			$mutation = $this->mutationRepository->getByIdChecked($data->mutation);
			$role = $data->role ?? null;
			$newUser = $this->userModel->create($mutation, $data->email, role: $role);

		} catch (UniqueConstraintViolationException $exception) {
			$this->flashMessage('Tento uživatel již existuje', 'error');

		} catch (Throwable $exception) {
			bd($exception);
			$this->flashMessage('Akce se nezdařila', 'error');
		}

		if ($newUser !== null) {
			$this->presenter->redirect('edit', ['id' => $newUser->id]);
		}
	}

}
